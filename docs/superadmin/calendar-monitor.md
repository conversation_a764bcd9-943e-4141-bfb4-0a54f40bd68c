# Calendar Token Health Monitoring - Design Specification

## Overview

This document outlines the design for extending the Super-Admin UI with calendar token health monitoring capabilities. The new monitoring dashboard will provide operations teams with real-time visibility into calendar integration health, token status, and provider performance across all tenants.

## Route Design

### New Route
- **Path**: `/superadmin/calendar`
- **Navigation**: Add "Calendar Health" to the existing superadmin sidebar navigation
- **Icon**: Calendar icon (from Lucide React)
- **Position**: Between "Models" and existing navigation items

## Widgets & KPIs

The calendar monitoring dashboard will display the following key performance indicators:

### 1. Token Health Overview
- **Widget**: Status cards showing overall token health
- **Metrics**:
  - `calendar_tokens_total{status="active"}` - Active tokens count
  - `calendar_tokens_total{status="expired"}` - Expired tokens count  
  - `calendar_tokens_total{status="expiring_soon"}` - Tokens expiring within 7 days
  - `calendar_tokens_total{status="invalid"}` - Invalid/revoked tokens count

### 2. Provider Performance
- **Widget**: Provider-specific health cards
- **Metrics**:
  - `calendar_api_requests_total{provider="google",status="success"}` - Successful API calls
  - `calendar_api_requests_total{provider="outlook",status="error"}` - Failed API calls
  - `calendar_api_response_time{provider="google",quantile="0.95"}` - 95th percentile response time
  - `calendar_rate_limit_hits_total{provider="calendly"}` - Rate limit violations

### 3. Token Expiration Timeline
- **Widget**: Timeline chart showing upcoming token expirations
- **Metrics**:
  - `calendar_token_expiry_days{provider="google",tenant_id="tenant123"}` - Days until expiration per token
  - Custom aggregation showing expiration distribution over next 30 days

### 4. Error Rate Trends
- **Widget**: Time series chart showing error rates over time
- **Metrics**:
  - `rate(calendar_api_errors_total[5m])` - Error rate per minute
  - `calendar_auth_failures_total{provider="outlook"}` - Authentication failures
  - `calendar_webhook_failures_total{provider="calendly"}` - Webhook delivery failures

### 5. Tenant Health Summary
- **Widget**: Table showing per-tenant calendar health status
- **Metrics**:
  - `calendar_tenant_health_score{tenant_id="tenant123"}` - Computed health score (0-100)
  - `calendar_last_successful_sync{tenant_id="tenant123"}` - Last successful calendar sync timestamp
  - `calendar_connected_providers{tenant_id="tenant123"}` - Number of connected providers per tenant

## Wireframe Layout

### ASCII Layout
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Calendar Health Monitoring                                    🔄 Refresh    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│ │   Active    │ │  Expired    │ │ Expiring    │ │  Invalid    │             │
│ │   Tokens    │ │   Tokens    │ │   Soon      │ │   Tokens    │             │
│ │     847     │ │      12     │ │     23      │ │      3      │             │
│ │   🟢 Good   │ │   🔴 Alert  │ │  🟡 Warning │ │   🔴 Error  │             │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘             │
│                                                                             │
│ ┌─────────────────────────────────┐ ┌─────────────────────────────────────┐ │
│ │        Provider Health          │ │      Token Expiration Timeline     │ │
│ │                                 │ │                                     │ │
│ │ Google Calendar    🟢 Healthy   │ │     [Chart showing expiration       │ │
│ │ • 423 active tokens             │ │      distribution over next        │ │
│ │ • 99.2% uptime                  │ │      30 days with bars for          │ │
│ │ • 245ms avg response            │ │      each day]                      │ │
│ │                                 │ │                                     │ │
│ │ Outlook           🟡 Degraded   │ │                                     │ │
│ │ • 312 active tokens             │ │                                     │ │
│ │ • 94.1% uptime                  │ │                                     │ │
│ │ • 1.2s avg response             │ │                                     │ │
│ │                                 │ │                                     │ │
│ │ Calendly          🟢 Healthy    │ │                                     │ │
│ │ • 156 active tokens             │ │                                     │ │
│ │ • 98.7% uptime                  │ │                                     │ │
│ │ • 180ms avg response            │ │                                     │ │
│ └─────────────────────────────────┘ └─────────────────────────────────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                        Error Rate Trends (Last 24h)                    │ │
│ │                                                                         │ │
│ │     [Time series line chart showing error rates for each provider      │ │
│ │      over the last 24 hours with different colored lines]              │ │
│ │                                                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                         Tenant Health Summary                          │ │
│ │ ┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐ │
│ │ │ Tenant      │ Health      │ Providers   │ Last Sync   │ Issues      │ │
│ │ │ Name        │ Score       │ Connected   │             │             │ │
│ │ ├─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤ │
│ │ │ Acme Law    │ 98 🟢       │ 3/3         │ 2 min ago   │ None        │ │
│ │ │ Smith & Co  │ 76 🟡       │ 2/3         │ 15 min ago  │ Token exp.  │ │
│ │ │ Legal Plus  │ 45 🔴       │ 1/3         │ 2 hours ago │ Auth failed │ │
│ │ └─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Mermaid Component Diagram
```mermaid
graph TB
    A[Calendar Health Monitoring Page] --> B[Header with Refresh Button]
    A --> C[Token Status Cards Row]
    A --> D[Middle Section Row]
    A --> E[Error Trends Chart]
    A --> F[Tenant Health Table]

    C --> C1[Active Tokens Card]
    C --> C2[Expired Tokens Card]
    C --> C3[Expiring Soon Card]
    C --> C4[Invalid Tokens Card]

    D --> D1[Provider Health Panel]
    D --> D2[Expiration Timeline Chart]

    D1 --> D1A[Google Calendar Status]
    D1 --> D1B[Outlook Status]
    D1 --> D1C[Calendly Status]

    F --> F1[Tenant Rows with Health Scores]
    F --> F2[Pagination Controls]

    style C1 fill:#10B981
    style C2 fill:#EF4444
    style C3 fill:#F59E0B
    style C4 fill:#EF4444
```

## Technical Implementation Notes

### Data Sources
- **Prometheus Metrics**: Primary source for real-time metrics
- **Supabase Database**: Token metadata and tenant information
- **Auth Service**: Token validation and refresh status
- **Calendar Providers**: Direct health check endpoints

### Refresh Strategy
- **Auto-refresh**: Every 30 seconds for critical metrics
- **Manual refresh**: Button to force immediate update
- **Background polling**: Separate service to collect metrics

### Performance Considerations
- **Caching**: Cache computed health scores for 1 minute
- **Pagination**: Tenant table supports pagination for large datasets
- **Lazy loading**: Charts load data on-demand when visible

## Access Control

### Role Requirements
- **Required Role**: `superadmin` (same as other superadmin pages)
- **Implementation**: Use existing `RoleBasedComponent` wrapper
- **Fallback**: Redirect to login if unauthorized

### Code Pattern
```typescript
// Following existing superadmin page patterns
export default function CalendarMonitorPage() {
  const router = useRouter();
  
  useEffect(() => {
    async function checkAccess() {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        router.push('/login');
        return;
      }
      // Additional role check would be implemented here
    }
    checkAccess();
  }, [router]);
  
  return (
    <RoleBasedComponent allowedRoles={['superadmin']}>
      {/* Calendar monitoring content */}
    </RoleBasedComponent>
  );
}
```

## UI Component Patterns

### Following Existing Patterns
- **Card Layout**: Use existing `Card`, `CardHeader`, `CardTitle`, `CardContent` components
- **Status Indicators**: Consistent with security dashboard (🟢🟡🔴 indicators)
- **Tables**: Use existing `Table`, `TableHeader`, `TableBody` components
- **Charts**: Integrate with existing Recharts library (used in resource usage page)
- **Loading States**: Use `Loader2` component with spin animation
- **Tabs**: Use `Tabs`, `TabsList`, `TabsTrigger` for organizing different views

### Color Scheme
- **Success**: Green (#10B981) - Healthy status
- **Warning**: Yellow (#F59E0B) - Attention needed
- **Error**: Red (#EF4444) - Critical issues
- **Info**: Blue (#3B82F6) - Informational status

## Integration Points

### Metrics Collection
- Extend existing `MetricsCollector` service to include calendar-specific metrics
- Add calendar health check endpoints to existing health service
- Implement token expiration monitoring in Auth Service

### Navigation Update
- Add calendar monitoring link to `frontend/src/app/superadmin/layout.tsx`
- Update navigation items array with calendar icon and route

This design provides operations teams with comprehensive visibility into calendar integration health while maintaining consistency with existing superadmin UI patterns and access controls.
