# Superadmin Documentation

This directory contains documentation for the Superadmin interface and related administrative functions.

## Table of Contents

### User Guides
- [Superadmin User Guide](superadmin-user-guide.md) - Complete guide for using the superadmin interface
- [Superadmin Dashboard](superadmin-dashboard.md) - Overview of the main dashboard features

### Feature Documentation
- [LLM Model Management](superadmin-llm-ui.md) - Managing LLM models and configurations
- [Prompt Management](superadmin-prompt-model-management.md) - Managing prompts and model assignments
- [Calendar Token Health Monitoring](superadmin/calendar-monitor.md) - Monitoring calendar integration health

### Technical Specifications
- [Calendar Monitor Design](superadmin/calendar-monitor.md) - Design specification for calendar token health monitoring

## Quick Access

### Main Features
1. **Dashboard** (`/superadmin`) - System overview and key metrics
2. **Security** (`/superadmin/security`) - Security events and monitoring
3. **Users** (`/superadmin/users`) - User management and roles
4. **Tenant Quotas** (`/superadmin/tenant-quotas`) - Resource allocation management
5. **Resource Usage** (`/superadmin/resource-usage`) - Usage monitoring and analytics
6. **Prompts** (`/superadmin/prompts`) - Prompt template management
7. **Models** (`/superadmin/models`) - LLM model configuration
8. **Calendar Health** (`/superadmin/calendar`) - Calendar integration monitoring *(planned)*

### Access Requirements
- **Role**: `superadmin`
- **Authentication**: Valid JWT token with superadmin role
- **MFA**: Multi-factor authentication recommended

## Getting Started

1. **Login**: Navigate to `/login` and authenticate with superadmin credentials
2. **Dashboard**: Review system overview and alerts
3. **Navigation**: Use the collapsible sidebar to access different sections
4. **Monitoring**: Regularly check security events and resource usage

## Support

For technical issues or questions:
- Check the relevant documentation section
- Contact the development team
- Submit a support ticket for urgent issues

## Contributing

When adding new superadmin features:
1. Follow existing UI patterns and components
2. Implement proper role-based access control
3. Add comprehensive documentation
4. Update this table of contents
