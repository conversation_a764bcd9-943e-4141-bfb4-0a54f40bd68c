# Calendar CRUD API Reference

This document provides a detailed reference for the Calendar CRUD API endpoints.

## Base URL

The base URL for all API endpoints is:

```
https://api.ailex.com/calendar
```

## Authentication

All API endpoints (except for OAuth-related endpoints) require authentication using a JWT token. The token should be included in the `Authorization` header:

```
Authorization: Bearer <token>
```

The JWT token should include the following claims:
- `sub`: User ID
- `tenant_id`: Firm/Tenant ID
- `role`: User role

## Endpoints

### OAuth Integration

#### Connect Calendar

```
GET /connect
```

Redirects to the Auth Service for OAuth consent.

**Query Parameters:**
- `provider` (required): The calendar provider (google, calendly, etc.)
- `redirect_uri` (required): Redirect URI after OAuth flow

**Response:**
- Redirects to Auth Service

#### OAuth Callback

```
GET /callback
```

Handles the OAuth callback from the Auth Service.

**Query Parameters:**
- `state` (required): State parameter for CSRF protection
- `code` (optional): Authorization code (if successful)
- `error` (optional): Error message (if failed)

**Response:**
- Redirects to the original redirect URI with success or error parameters

### Calendars

#### List Calendars

```
GET /calendars
```

Returns a list of calendars available for the authenticated user.

**Query Parameters:**
- `provider` (optional): Filter by provider (google, outlook, calendly)

**Response:**
```json
{
  "calendars": [
    {
      "id": "primary",
      "name": "My Calendar",
      "description": "My primary calendar",
      "timezone": "America/New_York",
      "primary": true,
      "provider": "google",
      "access_role": "owner"
    }
  ]
}
```

#### Get Calendar

```
GET /calendars/{calendar_id}
```

Returns details of a specific calendar.

**Path Parameters:**
- `calendar_id` (required): The calendar ID

**Response:**
```json
{
  "id": "primary",
  "name": "My Calendar",
  "description": "My primary calendar",
  "timezone": "America/New_York",
  "primary": true,
  "provider": "google",
  "access_role": "owner"
}
```

### Events

#### List Events

```
GET /events
```

Returns a list of events from a calendar.

**Query Parameters:**
- `calendar_id` (required): The calendar ID
- `start_time` (required): Start of the time range (ISO format)
- `end_time` (required): End of the time range (ISO format)
- `max_results` (optional): Maximum number of events to return (default: 100)

**Response:**
```json
{
  "events": [
    {
      "id": "event123",
      "calendar_id": "primary",
      "summary": "Meeting with Client",
      "description": "Discuss project requirements",
      "location": "Office",
      "start_time": "2023-06-01T09:00:00Z",
      "end_time": "2023-06-01T10:00:00Z",
      "all_day": false,
      "attendees": [
        {
          "email": "<EMAIL>",
          "name": "Client Name",
          "response_status": "accepted",
          "is_organizer": false
        }
      ],
      "organizer": {
        "email": "<EMAIL>",
        "name": "My Name",
        "is_organizer": true
      },
      "status": "confirmed",
      "created_at": "2023-05-20T10:00:00Z",
      "updated_at": "2023-05-20T10:30:00Z",
      "provider": "google",
      "provider_event_link": "https://www.google.com/calendar/event?eid=123"
    }
  ]
}
```

#### Get Event

```
GET /events/{event_id}
```

Returns details of a specific event.

**Path Parameters:**
- `event_id` (required): The event ID

**Query Parameters:**
- `calendar_id` (required): The calendar ID

**Response:**
```json
{
  "id": "event123",
  "calendar_id": "primary",
  "summary": "Meeting with Client",
  "description": "Discuss project requirements",
  "location": "Office",
  "start_time": "2023-06-01T09:00:00Z",
  "end_time": "2023-06-01T10:00:00Z",
  "all_day": false,
  "attendees": [
    {
      "email": "<EMAIL>",
      "name": "Client Name",
      "response_status": "accepted",
      "is_organizer": false
    }
  ],
  "organizer": {
    "email": "<EMAIL>",
    "name": "My Name",
    "is_organizer": true
  },
  "status": "confirmed",
  "created_at": "2023-05-20T10:00:00Z",
  "updated_at": "2023-05-20T10:30:00Z",
  "provider": "google",
  "provider_event_link": "https://www.google.com/calendar/event?eid=123"
}
```

#### Create Event

```
POST /events
```

Creates a new event in a calendar. Automatically checks for conflicts with existing events.

**Request Body:**
```json
{
  "calendar_id": "primary",
  "summary": "Meeting with Client",
  "description": "Discuss project requirements",
  "location": "Office",
  "start_time": "2023-06-01T09:00:00Z",
  "end_time": "2023-06-01T10:00:00Z",
  "all_day": false,
  "attendees": [
    {
      "email": "<EMAIL>",
      "name": "Client Name"
    }
  ],
  "check_conflicts": true,
  "buffer_minutes": 0
}
```

**Response:**
```json
{
  "id": "event123",
  "calendar_id": "primary",
  "summary": "Meeting with Client",
  "description": "Discuss project requirements",
  "location": "Office",
  "start_time": "2023-06-01T09:00:00Z",
  "end_time": "2023-06-01T10:00:00Z",
  "all_day": false,
  "attendees": [
    {
      "email": "<EMAIL>",
      "name": "Client Name",
      "response_status": "needsAction",
      "is_organizer": false
    }
  ],
  "organizer": {
    "email": "<EMAIL>",
    "name": "My Name",
    "is_organizer": true
  },
  "status": "confirmed",
  "created_at": "2023-05-20T10:00:00Z",
  "updated_at": "2023-05-20T10:00:00Z",
  "provider": "google",
  "provider_event_link": "https://www.google.com/calendar/event?eid=123",
  "conflicts": {
    "has_conflicts": false,
    "conflicts": []
  }
}
```

**Notes:**
- The `check_conflicts` parameter is optional (default: true) and determines whether to check for conflicts with existing events.
- The `buffer_minutes` parameter is optional (default: 0) and specifies a buffer time in minutes to add before and after the event when checking for conflicts.
- For Calendly, which doesn't support direct event creation, a scheduling link will be returned instead.
- Google Calendar and Microsoft Outlook support full CRUD operations and conflict checking.
- Conflict checking requires the provider to support free/busy checking.

#### Update Event

```
PUT /events/{event_id}
```

Updates an existing event in a calendar.

**Path Parameters:**
- `event_id` (required): The event ID

**Query Parameters:**
- `calendar_id` (required): The calendar ID

**Request Body:**
```json
{
  "summary": "Updated Meeting with Client",
  "description": "Updated description",
  "location": "Updated location",
  "start_time": "2023-06-01T10:00:00Z",
  "end_time": "2023-06-01T11:00:00Z",
  "all_day": false,
  "attendees": [
    {
      "email": "<EMAIL>",
      "name": "Client Name"
    },
    {
      "email": "<EMAIL>",
      "name": "Colleague Name"
    }
  ]
}
```

**Response:**
```json
{
  "id": "event123",
  "calendar_id": "primary",
  "summary": "Updated Meeting with Client",
  "description": "Updated description",
  "location": "Updated location",
  "start_time": "2023-06-01T10:00:00Z",
  "end_time": "2023-06-01T11:00:00Z",
  "all_day": false,
  "attendees": [
    {
      "email": "<EMAIL>",
      "name": "Client Name",
      "response_status": "needsAction",
      "is_organizer": false
    },
    {
      "email": "<EMAIL>",
      "name": "Colleague Name",
      "response_status": "needsAction",
      "is_organizer": false
    }
  ],
  "organizer": {
    "email": "<EMAIL>",
    "name": "My Name",
    "is_organizer": true
  },
  "status": "confirmed",
  "created_at": "2023-05-20T10:00:00Z",
  "updated_at": "2023-05-20T11:00:00Z",
  "provider": "google",
  "provider_event_link": "https://www.google.com/calendar/event?eid=123"
}
```

#### Delete Event

```
DELETE /events/{event_id}
```

Deletes an event from a calendar.

**Path Parameters:**
- `event_id` (required): The event ID

**Query Parameters:**
- `calendar_id` (required): The calendar ID

**Response:**
```json
{
  "success": true
}
```

### Free/Busy

#### Check Free/Busy

```
POST /free-busy
```

Checks free/busy times for calendars. This endpoint is used by the conflict checking functionality.

**Request Body:**
```json
{
  "provider": "google",
  "calendar_ids": ["primary"],
  "start_time": "2023-06-01T09:00:00Z",
  "end_time": "2023-06-01T17:00:00Z",
  "timezone": "UTC"
}
```

**Provider Notes:**
- Google Calendar: Fully supports free/busy checking and all CRUD operations
- Microsoft Outlook: Fully supports free/busy checking and all CRUD operations via Microsoft Graph API
- Calendly: Supports free/busy checking but has limited capabilities for event creation (uses scheduling links instead)

**Response:**
```json
{
  "calendars": {
    "primary": [
      {
        "start": "2023-06-01T09:00:00Z",
        "end": "2023-06-01T10:00:00Z"
      },
      {
        "start": "2023-06-01T13:00:00Z",
        "end": "2023-06-01T14:00:00Z"
      }
    ]
  },
  "time_min": "2023-06-01T09:00:00Z",
  "time_max": "2023-06-01T17:00:00Z"
}
```

## Error Responses

All API endpoints return appropriate HTTP status codes and error messages in case of errors:

```json
{
  "error": {
    "code": "invalid_request",
    "message": "Invalid request: calendar_id is required"
  }
}
```

Common error codes:
- `invalid_request`: Invalid request parameters
- `authentication_error`: Authentication failed
- `permission_error`: Permission denied
- `not_found`: Resource not found
- `rate_limit_exceeded`: Rate limit exceeded
- `provider_error`: Error from the calendar provider
- `internal_error`: Internal server error

## Webhooks

The Calendar CRUD Agent supports webhooks for event changes. Webhooks are fired to the Voice Receptionist for events with `source=ivr`.

### Webhook Payload

```json
{
  "event_type": "event.created",
  "event_id": "event123",
  "calendar_id": "primary",
  "summary": "Meeting with Client",
  "start_time": "2023-06-01T09:00:00Z",
  "end_time": "2023-06-01T10:00:00Z",
  "provider": "google",
  "provider_event_link": "https://www.google.com/calendar/event?eid=123",
  "source": "ivr",
  "call_id": "call123",
  "timestamp": "2023-05-20T10:00:00Z"
}
```

## Rate Limiting

The Calendar CRUD API implements rate limiting to prevent abuse. Rate limits are applied per firm and per provider, based on the provider's rate limits.

Rate limit headers are included in all API responses:
- `X-RateLimit-Limit`: The maximum number of requests allowed in the current time window
- `X-RateLimit-Remaining`: The number of requests remaining in the current time window
- `X-RateLimit-Reset`: The time when the current time window resets (Unix timestamp)

## Conclusion

This API reference provides a comprehensive guide to the Calendar CRUD API endpoints. The API includes features such as:

- Calendar event CRUD operations
- Free/busy checking
- Conflict detection when creating events
- Provider-specific capabilities (Google Calendar, Microsoft Outlook, Calendly)
- Live integration testing for Outlook provider
- Webhook integration with Voice Receptionist

For more information, see the [Architecture Document](architecture.md) and [Provider Capabilities](provider_capabilities.md).
