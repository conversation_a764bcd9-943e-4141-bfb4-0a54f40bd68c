# Live Integration Tests for Calendar Providers

This directory contains live integration tests that hit real external endpoints to verify the calendar provider integrations are working correctly.

## Overview

Live tests are different from unit tests in that they:
- Hit real external APIs (Auth Service, Microsoft Graph API, etc.)
- Require valid authentication tokens
- May be subject to rate limits
- Can fail due to network issues or service outages
- Are slower to execute

## Outlook Live Tests

### What They Test

The Outlook live tests (`test_outlook_live.py`) verify:

1. **Auth Service Integration**: 
   - Calls `https://ailex-auth.fly.dev/v1/tokens/TEST_FIRM/outlook`
   - Verifies token retrieval works correctly

2. **Microsoft Graph API Integration**:
   - Uses the retrieved token to call Microsoft Graph API
   - Specifically tests `/me/calendar/getSchedule` endpoint
   - Verifies authentication and API response structure

3. **Service Health**:
   - Checks that the Auth Service is reachable
   - Verifies expected response codes (200, 401, 404)

### Running the Tests

#### Prerequisites

- Network connectivity to external services
- Valid test firm configuration in the Auth Service (optional for health checks)

#### Method 1: Using the Test Runner Script

```bash
# Run live tests
OUTLOOK_LIVE=1 python run_outlook_live_tests.py
```

#### Method 2: Using pytest directly

```bash
# Run only Outlook live tests
OUTLOOK_LIVE=1 pytest test_outlook_live.py -v -m slow

# Run all slow tests (includes live tests)
OUTLOOK_LIVE=1 pytest -v -m slow

# Run with more verbose output
OUTLOOK_LIVE=1 pytest test_outlook_live.py -v -s -m slow
```

#### Method 3: From the project root

```bash
# From the project root directory
cd /path/to/pi_lawyer_ai
OUTLOOK_LIVE=1 pytest backend/agents/interactive/calendar_crud/providers/tests/test_outlook_live.py -v -m slow
```

### Environment Variables

- `OUTLOOK_LIVE=1`: **Required** to enable live tests. Without this, tests are skipped.

### Test Markers

- `@pytest.mark.slow`: Marks tests as slow-running
- `@pytest.mark.asyncio`: Marks tests as async

### Expected Outcomes

#### Successful Test Run
```
✅ Live Outlook integration test passed!
   - Token retrieved successfully from https://ailex-auth.fly.dev/v1/tokens/TEST_FIRM/outlook
   - Microsoft Graph API call successful to https://graph.microsoft.com/v1.0/me/calendar/getSchedule
   - Retrieved schedule data for 1 calendar(s)

✅ Auth Service health check passed!
   - Service is reachable at https://ailex-auth.fly.dev
   - Returned expected status code: 200
```

#### Skipped Tests (when OUTLOOK_LIVE is not set)
```
Results (0.46s):
       2 skipped
```

#### Failed Tests (various reasons)
- **Network issues**: "Request timed out - check network connectivity"
- **Auth issues**: "Token retrieval failed with status 401"
- **Service down**: "Auth Service health check timed out"

## CI/CD Integration

### GitHub Actions

Live tests are **not** included in the regular CI pipeline to:
- Avoid hitting rate limits
- Prevent failures due to external service issues
- Keep CI fast and reliable

### Manual CI Runs

To run live tests in CI manually:

```yaml
# In GitHub Actions workflow
- name: Run Live Integration Tests
  env:
    OUTLOOK_LIVE: 1
  run: |
    pytest backend/agents/interactive/calendar_crud/providers/tests/test_outlook_live.py -v -m slow
```

## Troubleshooting

### Common Issues

1. **Tests are skipped**
   - Ensure `OUTLOOK_LIVE=1` is set
   - Check that pytest is finding the test file

2. **Network timeouts**
   - Check internet connectivity
   - Verify external services are operational
   - Try increasing timeout values in the test

3. **Authentication failures**
   - Verify TEST_FIRM exists in Auth Service
   - Check if tokens are properly configured
   - Ensure OAuth permissions are granted

4. **Rate limiting**
   - Wait before retrying
   - Check provider rate limits
   - Consider using different test data

### Debug Mode

For more detailed output:

```bash
OUTLOOK_LIVE=1 pytest test_outlook_live.py -v -s --tb=long -m slow
```

## Adding New Live Tests

When adding new live tests:

1. **Mark with appropriate decorators**:
   ```python
   @pytest.mark.slow
   @pytest.mark.asyncio
   async def test_new_live_integration(self):
   ```

2. **Gate behind environment variable**:
   ```python
   pytestmark = pytest.mark.skipif(
       os.getenv("PROVIDER_LIVE") != "1",
       reason="Live tests require PROVIDER_LIVE=1"
   )
   ```

3. **Use appropriate timeouts**:
   ```python
   async with httpx.AsyncClient(timeout=30.0) as client:
   ```

4. **Handle errors gracefully**:
   ```python
   try:
       response = await client.get(url)
   except httpx.TimeoutException:
       pytest.fail("Request timed out")
   except httpx.RequestError as e:
       pytest.fail(f"Network error: {str(e)}")
   ```

5. **Provide helpful output**:
   ```python
   print(f"✅ Test passed!")
   print(f"   - Service responded with status {response.status_code}")
   ```

## Security Considerations

- Live tests may expose API endpoints in logs
- Avoid logging sensitive data (tokens, personal information)
- Use test-specific endpoints when possible
- Ensure test data doesn't contain real user information
