#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run live Outlook integration tests.

This script demonstrates how to run the live Outlook integration tests
that hit real endpoints. These tests are gated behind the OUTLOOK_LIVE=1
environment variable to prevent accidental execution.

Usage:
    # Run live tests (requires OUTLOOK_LIVE=1)
    OUTLOOK_LIVE=1 python run_outlook_live_tests.py
    
    # Run with pytest directly
    OUTLOOK_LIVE=1 pytest test_outlook_live.py -v -m slow
    
    # Run all slow tests
    OUTLOOK_LIVE=1 pytest -v -m slow
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Run the live Outlook integration tests."""
    
    # Check if OUTLOOK_LIVE is set
    if os.getenv("OUTLOOK_LIVE") != "1":
        print("❌ OUTLOOK_LIVE environment variable is not set to '1'")
        print("   Live tests are disabled to prevent hitting real endpoints.")
        print("   To run live tests, set OUTLOOK_LIVE=1:")
        print("   ")
        print("   OUTLOOK_LIVE=1 python run_outlook_live_tests.py")
        print("   ")
        return 1
    
    print("🚀 Running live Outlook integration tests...")
    print("   This will hit real endpoints:")
    print("   - https://ailex-auth.fly.dev/v1/tokens/TEST_FIRM/outlook")
    print("   - https://graph.microsoft.com/v1.0/me/calendar/getSchedule")
    print("   ")
    
    # Get the directory containing this script
    script_dir = Path(__file__).parent
    
    # Run pytest with the live test file
    cmd = [
        sys.executable, "-m", "pytest",
        str(script_dir / "test_outlook_live.py"),
        "-v",  # Verbose output
        "-s",  # Don't capture output (so we can see print statements)
        "-m", "slow",  # Only run slow tests
        "--tb=short",  # Short traceback format
    ]
    
    try:
        result = subprocess.run(cmd, cwd=script_dir)
        return result.returncode
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
